using System;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using TeklaTool.Models;
using TeklaTool.Services;
using TeklaTool.Utils;

namespace TeklaTool.ViewModels
{
    public class PartListViewModel : OptimizedViewModelBase
    {
        private readonly TeklaModelService _teklaModelService;
        private readonly MainViewModel _mainViewModel;
        private OptimizedObservableCollection<TeklaModelPart> _parts = new OptimizedObservableCollection<TeklaModelPart>();
        private OptimizedObservableCollection<MergedPartRow> _mergedParts = new OptimizedObservableCollection<MergedPartRow>();
        private bool _isMergeRows;
        public OptimizedObservableCollection<TeklaModelPart> Parts => _parts;
        public OptimizedObservableCollection<MergedPartRow> MergedParts => _mergedParts;
        public bool IsMergeRows
        {
            get => _isMergeRows;
            set
            {
                if (SetProperty(ref _isMergeRows, value))
                {
                    UpdateMergedParts();
                    OnPropertyChanged(nameof(PartsView));
                }
            }
        }
        public IEnumerable<object> PartsView => IsMergeRows ? (IEnumerable<object>)MergedParts : Parts;
        public PartListViewModel(TeklaModelService service, MainViewModel mainViewModel = null)
        {
            _teklaModelService = service;
            _mainViewModel = mainViewModel;
            Parts.CollectionChanged += (s, e) => { if (IsMergeRows) UpdateMergedParts(); };
        }
        public void SetParts(IEnumerable<TeklaModelPart> parts)
        {
            SuspendUpdates();
            try
            {
                Parts.ReplaceAll(parts);
                if (IsMergeRows) UpdateMergedParts();
            }
            finally
            {
                ResumeUpdates();
            }
        }
        private async void UpdateMergedParts()
        {
            // 如果未开启合并行，直接返回
            if (!IsMergeRows) return;

            // 如果没有零件数据，直接返回
            if (Parts == null || Parts.Count == 0)
            {
                MergedParts.Clear();
                return;
            }

            try
            {
                // 在后台线程中进行分组计算
                var grouped = await Task.Run(() =>
                {
                    return Parts.GroupBy(p => p.PartNumber)
                        .Select((g, idx) => new MergedPartRow
                        {
                            Index = idx + 1,
                            PartNumber = g.Key,
                            Name = g.First().Name,
                            Profile = g.First().Profile,
                            Material = g.First().Material,
                            Finish = g.First().Finish,
                            Class = g.First().Class,
                            Phase = g.First().Phase,
                            Count = g.Count(),
                            AssemblyNumber = g.First().AssemblyNumber,
                            Guid = g.First().Guid,
                            ModelObjectIds = g.Where(x => x.ModelObjectId.HasValue).Select(x => x.ModelObjectId.Value).ToList(),
                            Remark = g.First().Remark
                        }).ToList();
                });

                // 批量更新合并行集合
                MergedParts.ReplaceAll(grouped);

                // 通知视图更新
                OnPropertyChanged(nameof(PartsView));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新合并行时发生错误: {ex.Message}");
            }
        }

        // 选择变更逻辑入口
        public void HandlePartSelectionChanged(IList<TeklaModelPart> selectedParts)
        {
            // 直接调用高亮方法
            HighlightParts(selectedParts);
        }

        // 高亮逻辑
        public void HighlightParts(IList<TeklaModelPart> selectedParts)
        {
            // 检查是否启用了高亮功能
            if (_mainViewModel != null && !_mainViewModel.EnableHighlight)
            {
                return; // 如果禁用了高亮功能，直接返回
            }

            if (selectedParts == null || selectedParts.Count == 0)
            {
                _teklaModelService.HighlightObjects(new List<int>());
                return;
            }

            // 根据是否开启了合并行来决定使用哪种高亮方式
            if (IsMergeRows)
            {
                // 合并行模式：使用零件编号高亮
                // 收集所有选中行的零件编号
                var partNumbers = selectedParts.Select(p => p.PartNumber).Distinct().ToList();

                // 找出所有具有这些零件编号的零件
                var allParts = Parts.Where(p => partNumbers.Contains(p.PartNumber)).ToList();

                // 提取这些零件的ID
                var modelObjectIds = allParts.Where(p => p.ModelObjectId.HasValue)
                                            .Select(p => p.ModelObjectId.Value)
                                            .ToList();

                if (modelObjectIds.Count > 0)
                {
                    _teklaModelService.HighlightObjects(modelObjectIds);
                }
            }
            else
            {
                // 未合并行模式：直接使用零件ID高亮
                var modelObjectIds = selectedParts.Where(p => p.ModelObjectId.HasValue)
                                                .Select(p => p.ModelObjectId.Value)
                                                .ToList();
                if (modelObjectIds.Count > 0)
                {
                    _teklaModelService.HighlightObjects(modelObjectIds);
                }
            }
        }

        /// <summary>
        /// 获取当前选中的零件（需要从UI层传入选中项）
        /// </summary>
        public List<TeklaModelPart> GetSelectedParts()
        {
            // 这个方法需要与UI层配合，通过事件或其他方式获取选中项
            // 暂时返回空列表，实际实现需要在UI层调用时传入选中项
            return new List<TeklaModelPart>();
        }

        /// <summary>
        /// 根据选中的合并行获取对应的零件
        /// </summary>
        public List<TeklaModelPart> GetPartsFromMergedRows(List<MergedPartRow> selectedRows)
        {
            if (selectedRows == null || selectedRows.Count == 0)
                return new List<TeklaModelPart>();

            var partNumbers = selectedRows.Select(r => r.PartNumber).ToHashSet();
            return Parts.Where(p => partNumbers.Contains(p.PartNumber)).ToList();
        }

        // 筛选逻辑迁移（示例：按名称筛选）
        public void FilterByName(string name)
        {
            var filtered = Parts.Where(p => p.Name != null && p.Name.Contains(name)).ToList();
            SetParts(filtered);
        }

        // 命令示例
        public ICommand FilterByNameCommand => new RelayCommand(param =>
        {
            if (param is string name)
                FilterByName(name);
        });

        public class MergedPartRow
        {
            public int Index { get; set; }
            public string PartNumber { get; set; }
            public string Name { get; set; }
            public string Profile { get; set; }
            public string Material { get; set; }
            public string Finish { get; set; }
            public string Class { get; set; }
            public string Phase { get; set; }
            public int Count { get; set; }
            public string AssemblyNumber { get; set; }
            public string Guid { get; set; }
            public List<int> ModelObjectIds { get; set; }
            public string Remark { get; set; }
        }
    }
}
