using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace TeklaTool.Utils
{
    /// <summary>
    /// 性能监控工具，用于监控和优化应用程序性能
    /// </summary>
    public static class PerformanceMonitor
    {
        private static readonly Dictionary<string, Stopwatch> _timers = new Dictionary<string, Stopwatch>();
        private static readonly Dictionary<string, List<long>> _measurements = new Dictionary<string, List<long>>();
        private static readonly object _lock = new object();
        private static bool _isEnabled = true;

        /// <summary>
        /// 启用或禁用性能监控
        /// </summary>
        public static bool IsEnabled
        {
            get => _isEnabled;
            set => _isEnabled = value;
        }

        /// <summary>
        /// 开始计时
        /// </summary>
        public static void StartTimer(string name)
        {
            if (!_isEnabled) return;

            lock (_lock)
            {
                if (_timers.ContainsKey(name))
                {
                    _timers[name].Restart();
                }
                else
                {
                    _timers[name] = Stopwatch.StartNew();
                }
            }
        }

        /// <summary>
        /// 停止计时并记录结果
        /// </summary>
        public static long StopTimer(string name)
        {
            if (!_isEnabled) return 0;

            lock (_lock)
            {
                if (_timers.TryGetValue(name, out var timer))
                {
                    timer.Stop();
                    var elapsed = timer.ElapsedMilliseconds;

                    // 记录测量结果
                    if (!_measurements.ContainsKey(name))
                    {
                        _measurements[name] = new List<long>();
                    }
                    _measurements[name].Add(elapsed);

                    // 保持最近100次测量
                    if (_measurements[name].Count > 100)
                    {
                        _measurements[name].RemoveAt(0);
                    }

                    Debug.WriteLine($"[性能监控] {name}: {elapsed}ms");
                    return elapsed;
                }
            }
            return 0;
        }

        /// <summary>
        /// 测量操作执行时间
        /// </summary>
        public static T MeasureTime<T>(string name, Func<T> operation)
        {
            if (!_isEnabled) return operation();

            StartTimer(name);
            try
            {
                return operation();
            }
            finally
            {
                StopTimer(name);
            }
        }

        /// <summary>
        /// 测量异步操作执行时间
        /// </summary>
        public static async Task<T> MeasureTimeAsync<T>(string name, Func<Task<T>> operation)
        {
            if (!_isEnabled) return await operation();

            StartTimer(name);
            try
            {
                return await operation();
            }
            finally
            {
                StopTimer(name);
            }
        }

        /// <summary>
        /// 测量操作执行时间（无返回值）
        /// </summary>
        public static void MeasureTime(string name, Action operation)
        {
            if (!_isEnabled)
            {
                operation();
                return;
            }

            StartTimer(name);
            try
            {
                operation();
            }
            finally
            {
                StopTimer(name);
            }
        }

        /// <summary>
        /// 测量异步操作执行时间（无返回值）
        /// </summary>
        public static async Task MeasureTimeAsync(string name, Func<Task> operation)
        {
            if (!_isEnabled)
            {
                await operation();
                return;
            }

            StartTimer(name);
            try
            {
                await operation();
            }
            finally
            {
                StopTimer(name);
            }
        }

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        public static Dictionary<string, PerformanceStats> GetStatistics()
        {
            var stats = new Dictionary<string, PerformanceStats>();

            lock (_lock)
            {
                foreach (var kvp in _measurements)
                {
                    var measurements = kvp.Value;
                    if (measurements.Count > 0)
                    {
                        var sum = 0L;
                        var min = long.MaxValue;
                        var max = long.MinValue;

                        foreach (var measurement in measurements)
                        {
                            sum += measurement;
                            if (measurement < min) min = measurement;
                            if (measurement > max) max = measurement;
                        }

                        stats[kvp.Key] = new PerformanceStats
                        {
                            Count = measurements.Count,
                            Average = (double)sum / measurements.Count,
                            Min = min,
                            Max = max,
                            Total = sum
                        };
                    }
                }
            }

            return stats;
        }

        /// <summary>
        /// 清除所有统计数据
        /// </summary>
        public static void ClearStatistics()
        {
            lock (_lock)
            {
                _measurements.Clear();
                _timers.Clear();
            }
        }

        /// <summary>
        /// 监控UI线程性能
        /// </summary>
        public static void MonitorUIThread()
        {
            if (!_isEnabled) return;

            var dispatcher = Application.Current?.Dispatcher;
            if (dispatcher == null) return;

            // 监控UI线程的响应性
            var timer = new DispatcherTimer(DispatcherPriority.Background)
            {
                Interval = TimeSpan.FromSeconds(1)
            };

            var lastCheck = DateTime.Now;
            timer.Tick += (s, e) =>
            {
                var now = DateTime.Now;
                var actualInterval = (now - lastCheck).TotalMilliseconds;
                lastCheck = now;

                // 如果实际间隔远大于预期间隔，说明UI线程可能被阻塞
                if (actualInterval > 1500) // 超过1.5秒
                {
                    Debug.WriteLine($"[性能监控] UI线程可能被阻塞，实际间隔: {actualInterval:F0}ms");
                }
            };

            timer.Start();
        }

        /// <summary>
        /// 监控内存使用情况
        /// </summary>
        public static void LogMemoryUsage(string context = "")
        {
            if (!_isEnabled) return;

            var process = Process.GetCurrentProcess();
            var workingSet = process.WorkingSet64 / 1024 / 1024; // MB
            var privateMemory = process.PrivateMemorySize64 / 1024 / 1024; // MB

            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var managedMemory = GC.GetTotalMemory(false) / 1024 / 1024; // MB

            Debug.WriteLine($"[内存监控] {context} - 工作集: {workingSet}MB, 私有内存: {privateMemory}MB, 托管内存: {managedMemory}MB");
        }
    }

    /// <summary>
    /// 性能统计信息
    /// </summary>
    public class PerformanceStats
    {
        public int Count { get; set; }
        public double Average { get; set; }
        public long Min { get; set; }
        public long Max { get; set; }
        public long Total { get; set; }

        public override string ToString()
        {
            return $"Count: {Count}, Avg: {Average:F1}ms, Min: {Min}ms, Max: {Max}ms, Total: {Total}ms";
        }
    }

    /// <summary>
    /// 性能监控的using语法糖
    /// </summary>
    public class PerformanceScope : IDisposable
    {
        private readonly string _name;
        private readonly bool _wasEnabled;

        public PerformanceScope(string name)
        {
            _name = name;
            _wasEnabled = PerformanceMonitor.IsEnabled;
            if (_wasEnabled)
            {
                PerformanceMonitor.StartTimer(_name);
            }
        }

        public void Dispose()
        {
            if (_wasEnabled)
            {
                PerformanceMonitor.StopTimer(_name);
            }
        }
    }
}
