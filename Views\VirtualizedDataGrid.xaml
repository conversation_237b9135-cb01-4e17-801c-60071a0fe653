<UserControl x:Class="TeklaTool.Views.VirtualizedDataGrid"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <DataGrid x:Name="MainDataGrid" 
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  CanUserReorderColumns="True"
                  CanUserResizeColumns="True"
                  CanUserResizeRows="False"
                  CanUserSortColumns="True"
                  SelectionMode="Extended"
                  SelectionUnit="FullRow"
                  GridLinesVisibility="All"
                  AlternatingRowBackground="AliceBlue"
                  HeadersVisibility="Column"
                  IsReadOnly="True"
                  VirtualizingPanel.IsVirtualizing="True"
                  VirtualizingPanel.VirtualizationMode="Recycling"
                  VirtualizingPanel.IsContainerVirtualizable="True"
                  VirtualizingPanel.ScrollUnit="Pixel"
                  EnableRowVirtualization="True"
                  EnableColumnVirtualization="True"
                  ScrollViewer.CanContentScroll="True"
                  ScrollViewer.IsDeferredScrollingEnabled="True">
            
            <!-- 优化的样式 -->
            <DataGrid.Resources>
                <!-- 优化行样式以提高性能 -->
                <Style TargetType="DataGridRow">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="BorderBrush" Value="Transparent"/>
                    <Setter Property="BorderThickness" Value="0"/>
                    <Style.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Background" Value="LightBlue"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="LightGray"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
                
                <!-- 优化单元格样式 -->
                <Style TargetType="DataGridCell">
                    <Setter Property="BorderBrush" Value="Transparent"/>
                    <Setter Property="BorderThickness" Value="0"/>
                    <Setter Property="Padding" Value="4,2"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="DataGridCell">
                                <Border Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        Padding="{TemplateBinding Padding}">
                                    <ContentPresenter VerticalAlignment="Center"/>
                                </Border>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </DataGrid.Resources>
            
            <!-- 列定义将在代码中动态生成 -->
        </DataGrid>
        
        <!-- 加载指示器 -->
        <Grid x:Name="LoadingOverlay" 
              Background="#80FFFFFF" 
              Visibility="Collapsed">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <TextBlock Text="正在加载数据..." FontSize="14" FontWeight="Bold" 
                          HorizontalAlignment="Center" Margin="0,0,0,10"/>
                <ProgressBar IsIndeterminate="True" Width="200" Height="10"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
