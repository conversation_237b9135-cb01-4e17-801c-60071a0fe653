using System;
using System.IO;
using Tekla.Structures.Model;

namespace TeklaTool.Models
{
    public class ModelConnection
    {
        private Model _currentModel = new Model();

        public Model CurrentModel => _currentModel;

        public bool Connect()
        {
            try
            {
                return _currentModel.GetConnectionStatus();
            }
            catch (Exception)
            {
                return false;
            }
        }

        public bool IsConnected()
        {
            return _currentModel != null && _currentModel.GetConnectionStatus();
        }
    }
}
